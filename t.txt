gene_type = float        
gene_type = int         
gene_type = numpy.int32  

gene_type = [float, 2]   
gene_type = [numpy.float32, 3] 
gene_type = [int, None]    


gene_type = [int, float, numpy.int32, numpy.float64, int]

gene_type = [
    [int, None],         
    [float, 2],           
    [numpy.int16, None], 
    [numpy.float32, 4],   
    [int, None]            
]





1.gene type 
2.discard_start_params
3.warnings