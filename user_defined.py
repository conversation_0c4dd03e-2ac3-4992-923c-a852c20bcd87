from typing import Any, Callable, Literal, Protocol




class ParentSelectionFunction(Protocol):
    """Protocol for custom parent selection functions in PyGAD."""
    
    def __call__(
        self, 
        fitness: NDArray[np.float64], 
        num_parents: int, 
        ga_instance: Any
    ) -> NDArray[np.int64]:
        pass


ParentSelectionLiteral = Literal["sss", "rws", "sus", "rank", "random", "tournament"]

ParentSelectionType = ParentSelectionLiteral | ParentSelectionFunction
