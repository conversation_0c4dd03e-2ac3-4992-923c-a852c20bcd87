(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ git checkout main
Switched to branch 'main'
Your branch is up to date with 'origin/main'.
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (main) $ git log
commit fe0dcf7b7fc5ff86872dd210fabefbbb320e25a2 (HEAD -> main, origin/main, origin/HEAD)
Author: <PERSON> <<EMAIL>>
Date:   Mon May 19 07:26:50 2025 -0400

    Add/fix links to github in a few more places (#598)

commit 57027100bc26350176052669d170927696bd4876
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Wed May 14 08:51:29 2025 +0200

    Polish new algorithms (#594)

commit 2100207f413132ce4f99808342a0c8b0160901f3
Author: <PERSON> <81754147+<PERSON>-<PERSON>hame<PERSON>@users.noreply.github.com>
Date:   Sat May 10 12:40:05 2025 +0530

    Infinite Gradient Handling (#582)

commit 1b0b1624f786033f27f006aa6470794c50800c26
Author: anTon <<EMAIL>>
Date:   Mon May 5 20:58:31 2025 +0530

(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (main) $ git logo
git: 'logo' is not a git command. See 'git --help'.

The most similar command is
        log
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (main) $ git checkout bayesian_optimizer 
Switched to branch 'bayesian_optimizer'
Your branch is up to date with 'origin/bayesian_optimizer'.
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ git log 
commit 9d41915c698bb1233f60b8e02a555c59c2442d6c (HEAD -> bayesian_optimizer, origin/bayesian_optimizer)
Author: spline2hg <<EMAIL>>
Date:   Sun Jul 6 16:38:46 2025 +0000

    fix: mypy version

commit 8bffc41ade65a37bcb65f4dca41f0213818cd40a
Merge: 4b419d6 9bb73a4
Author: Tim Mensinger <<EMAIL>>
Date:   Sun Jul 6 12:51:35 2025 +0200

    Merge branch 'main' into bayesian_optimizer

commit 4b419d6b15b51a669f597c1bf54451ad8a213960
Author: spline2hg <<EMAIL>>
Date:   Sat Jul 5 20:10:46 2025 +0000

    Add UnitIntervalFloat

commit 19ef2422d336f791a21b602cd525f11d50fcd780
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ git merge main
Merge made by the 'ort' strategy.
 .pre-commit-config.yaml                       |  8 +++-----
 src/optimagic/deprecations.py                 |  4 ++--
 src/optimagic/examples/criterion_functions.py | 15 +++++----------
 src/optimagic/logging/logger.py               |  2 +-
 4 files changed, 11 insertions(+), 18 deletions(-)
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ git log
commit 84c09d59e16cab8c8f7a944fde366ff45fd314b6 (HEAD -> bayesian_optimizer)
Merge: 9d41915 2418032
Author: spline2hg <<EMAIL>>
Date:   Mon Jul 7 23:32:39 2025 +0000

    Merge branch 'main' into bayesian_optimizer

commit 24180326cdde311c5d4b564ac0ecee37d7d0ad4c (origin/main, origin/HEAD, main)
Author: pre-commit-ci[bot] <66853113+pre-commit-ci[bot]@users.noreply.github.com>
Date:   Mon Jul 7 10:56:00 2025 +0200

    Update pre-commit hooks and fix mypy issues (#601)

(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ pre-commit run -a
[INFO] Initializing environment for https://github.com/PyCQA/docformatter.
[INFO] Initializing environment for https://github.com/astral-sh/ruff-pre-commit.
[INFO] Initializing environment for https://github.com/pre-commit/mirrors-mypy.
[INFO] Initializing environment for https://github.com/pre-commit/mirrors-mypy:numpy >= 2,packaging,pandas-stubs,sqlalchemy-stubs,types-cffi,types-openpyxl,types-jinja2.
[INFO] Installing environment for https://github.com/PyCQA/docformatter.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
[INFO] Installing environment for https://github.com/astral-sh/ruff-pre-commit.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
[INFO] Installing environment for https://github.com/pre-commit/mirrors-mypy.
[INFO] Once installed this environment will be reused.
[INFO] This may take a few minutes...
Check hooks apply to the repository......................................Passed
Check for useless excludes...............................................Passed
yamlfix..................................................................Passed
check environment file updates...........................................Passed
update algo selection code...............................................Passed
check for added large files..............................................Passed
check for case conflicts.................................................Passed
check for merge conflicts................................................Passed
check vcs permalinks.....................................................Passed
check yaml...............................................................Passed
check toml...............................................................Passed
debug statements (python)................................................Passed
fix end of files.........................................................Passed
fix utf-8 byte order marker..............................................Passed
forbid submodules....................................(no files to check)Skipped
mixed line ending........................................................Passed
python tests naming......................................................Passed
don't commit to branch...................................................Passed
trim trailing whitespace.................................................Passed
check python ast.........................................................Passed
yamllint.................................................................Passed
docformatter.............................................................Passed
ruff (legacy alias)......................................................Passed
ruff format..............................................................Passed
mdformat.................................................................Passed
mdformat.................................................................Passed
nbstripout...............................................................Passed
mypy.....................................................................Passed
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ pip show mypy
Name: mypy
Version: 1.15.0
Summary: Optional static typing for Python
Home-page: https://www.mypy-lang.org/
Author: 
Author-email: Jukka Lehtosalo <<EMAIL>>
License: MIT
Location: /opt/conda/envs/optimagic/lib/python3.10/site-packages
Requires: mypy_extensions, tomli, typing_extensions
Required-by: sqlalchemy-stubs
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ mypy
src/optimagic/optimizers/bayesian_optimizer.py:32: error: Cannot assign to a type  [misc]
src/optimagic/optimizers/bayesian_optimizer.py:32: error: Incompatible types in assignment (expression has type "<typing special form>", variable has type "type[BayesianOptimization]")  [assignment]
src/optimagic/optimizers/bayesian_optimizer.py:33: error: Cannot assign to a type  [misc]
src/optimagic/optimizers/bayesian_optimizer.py:33: error: Incompatible types in assignment (expression has type "<typing special form>", variable has type "type[AcquisitionFunction]")  [assignment]
Found 4 errors in 1 file (checked 241 source files)
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ pre-commit run -a
Check hooks apply to the repository......................................Passed
Check for useless excludes...............................................Passed
yamlfix..................................................................Passed
check environment file updates...........................................Failed
- hook id: update-environment-files
- files were modified by this hook
update algo selection code...............................................Passed
check for added large files..............................................Passed
check for case conflicts.................................................Passed
check for merge conflicts................................................Passed
check vcs permalinks.....................................................Passed
check yaml...............................................................Passed
check toml...............................................................Passed
debug statements (python)................................................Passed
fix end of files.........................................................Passed
fix utf-8 byte order marker..............................................Passed
forbid submodules....................................(no files to check)Skipped
mixed line ending........................................................Passed
python tests naming......................................................Passed
don't commit to branch...................................................Passed
trim trailing whitespace.................................................Passed
check python ast.........................................................Passed
yamllint.................................................................Passed
docformatter.............................................................Passed
ruff (legacy alias)......................................................Passed
ruff format..............................................................Passed
mdformat.................................................................Passed
mdformat.................................................................Passed
nbstripout...............................................................Passed
mypy.....................................................................Failed
- hook id: mypy
- files were modified by this hook

Success: no issues found in 236 source files

(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ mypy
src/optimagic/optimizers/bayesian_optimizer.py:32: error: Cannot assign to a type  [misc]
src/optimagic/optimizers/bayesian_optimizer.py:32: error: Incompatible types in assignment (expression has type "<typing special form>", variable has type "type[BayesianOptimization]")  [assignment]
src/optimagic/optimizers/bayesian_optimizer.py:33: error: Cannot assign to a type  [misc]
src/optimagic/optimizers/bayesian_optimizer.py:33: error: Incompatible types in assignment (expression has type "<typing special form>", variable has type "type[AcquisitionFunction]")  [assignment]
Found 4 errors in 1 file (checked 241 source files)
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ conda env update -f environment.yml --prune
/opt/conda/lib/python3.12/site-packages/conda/base/context.py:202: FutureWarning: Adding 'defaults' to channel list implicitly is deprecated and will be removed in 25.3. 

To remove this warning, please choose a default channel explicitly with conda's regular configuration system, e.g. by adding 'defaults' to the list of channels:

  conda config --add channels defaults

For more information see https://docs.conda.io/projects/conda/en/stable/user-guide/configuration/use-condarc.html

  deprecated.topic(
Retrieving notices: done
Channels:
 - conda-forge
Platform: linux-64
Collecting package metadata (repodata.json): done
Solving environment: done

Downloading and Extracting Packages:
                                                                                                                                
Preparing transaction: done
Verifying transaction: done
Executing transaction: done
Installing pip dependencies: / Ran pip subprocess with arguments:
['/opt/conda/envs/optimagic/bin/python', '-m', 'pip', 'install', '-U', '-r', '/workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt', '--exists-action=b']
Pip subprocess output:
Obtaining file:///workspaces/optimagic_ (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8))
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Checking if build backend supports build_editable: started
  Checking if build backend supports build_editable: finished with status 'done'
  Getting requirements to build editable: started
  Getting requirements to build editable: finished with status 'done'
  Installing backend dependencies: started
  Installing backend dependencies: finished with status 'done'
  Preparing editable metadata (pyproject.toml): started
  Preparing editable metadata (pyproject.toml): finished with status 'done'
Requirement already satisfied: bayesian-optimization>=2.0.4 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 1)) (3.0.1)
Requirement already satisfied: nevergrad in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 2)) (1.0.3)
Collecting nevergrad (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 2))
  Using cached nevergrad-1.0.12-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: DFO-LS>=1.5.3 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 3)) (1.5.4)
Requirement already satisfied: Py-BOBYQA in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 4)) (1.5.0)
Requirement already satisfied: fides==0.7.4 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 5)) (0.7.4)
Requirement already satisfied: kaleido in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 6)) (1.0.0)
Requirement already satisfied: pre-commit>=4 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (4.2.0)
Requirement already satisfied: pandas-stubs in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 9)) (2.3.0.250703)
Requirement already satisfied: types-cffi in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 10)) (1.17.0.20250523)
Requirement already satisfied: types-openpyxl in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 11)) (3.1.5.20250602)
Requirement already satisfied: types-jinja2 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 12)) (2.11.9)
Requirement already satisfied: sqlalchemy-stubs in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 13)) (0.4)
Requirement already satisfied: sphinxcontrib-mermaid in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.0.0)
Requirement already satisfied: pdbp in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from -r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 15)) (1.7.0)
Requirement already satisfied: annotated-types in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (0.7.0)
Requirement already satisfied: cloudpickle in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (3.1.1)
Requirement already satisfied: iminuit in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2.31.1)
Requirement already satisfied: joblib in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (1.5.1)
Requirement already satisfied: numpy in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2.2.6)
Requirement already satisfied: pandas in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2.3.0)
Requirement already satisfied: plotly<6.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (5.24.1)
Requirement already satisfied: pybaum>=0.1.2 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (0.1.3)
Requirement already satisfied: scipy>=1.2.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (1.15.2)
Requirement already satisfied: sqlalchemy>=1.3 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2.0.41)
Requirement already satisfied: typing-extensions in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (4.14.0)
Requirement already satisfied: h5py>=3.5.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from fides==0.7.4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 5)) (3.13.0)
Requirement already satisfied: tenacity>=6.2.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from plotly<6.0.0->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (9.1.2)
Requirement already satisfied: packaging in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from plotly<6.0.0->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (25.0)
Requirement already satisfied: colorama<1.0.0,>=0.4.6 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from bayesian-optimization>=2.0.4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 1)) (0.4.6)
Requirement already satisfied: scikit-learn<2.0.0,>=1.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from bayesian-optimization>=2.0.4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 1)) (1.7.0)
Requirement already satisfied: threadpoolctl>=3.1.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from scikit-learn<2.0.0,>=1.0.0->bayesian-optimization>=2.0.4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 1)) (3.6.0)
Requirement already satisfied: cma>=2.6.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from nevergrad->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 2)) (4.2.0)
INFO: pip is looking at multiple versions of nevergrad to determine which version is compatible with other requirements. This could take a while.
  Using cached nevergrad-1.0.11-py3-none-any.whl.metadata (11 kB)
  Using cached nevergrad-1.0.8-py3-none-any.whl.metadata (11 kB)
  Using cached nevergrad-1.0.5-py3-none-any.whl.metadata (11 kB)
  Using cached nevergrad-1.0.4-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: setuptools in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from DFO-LS>=1.5.3->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 3)) (80.9.0)
Requirement already satisfied: choreographer>=1.0.5 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from kaleido->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 6)) (1.0.9)
Requirement already satisfied: logistro>=1.0.8 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from kaleido->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 6)) (1.1.0)
Requirement already satisfied: orjson>=3.10.15 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from kaleido->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 6)) (3.10.18)
Requirement already satisfied: cfgv>=2.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (3.4.0)
Requirement already satisfied: identify>=1.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (2.6.12)
Requirement already satisfied: nodeenv>=0.11.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (1.9.1)
Requirement already satisfied: pyyaml>=5.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (6.0.2)
Requirement already satisfied: virtualenv>=20.10.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (20.31.2)
Requirement already satisfied: types-pytz>=2022.1.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pandas-stubs->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 9)) (2025.2.0.20250516)
Requirement already satisfied: types-setuptools in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from types-cffi->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 10)) (80.9.0.20250529)
Requirement already satisfied: types-MarkupSafe in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from types-jinja2->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 12)) (1.1.10)
Requirement already satisfied: mypy>=0.790 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sqlalchemy-stubs->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 13)) (1.14.1)
Requirement already satisfied: sphinx in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (4.5.0)
Requirement already satisfied: pygments>=2.19.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pdbp->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 15)) (2.19.1)
Requirement already satisfied: tabcompleter>=1.4.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pdbp->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 15)) (1.4.0)
Requirement already satisfied: simplejson>=3.19.3 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from choreographer>=1.0.5->kaleido->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 6)) (3.20.1)
Requirement already satisfied: mypy_extensions>=1.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from mypy>=0.790->sqlalchemy-stubs->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 13)) (1.1.0)
Requirement already satisfied: tomli>=1.1.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from mypy>=0.790->sqlalchemy-stubs->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 13)) (2.2.1)
Requirement already satisfied: greenlet>=1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sqlalchemy>=1.3->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (3.2.2)
Requirement already satisfied: distlib<1,>=0.3.7 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from virtualenv>=20.10.0->pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (0.3.9)
Requirement already satisfied: filelock<4,>=3.12.2 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from virtualenv>=20.10.0->pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (3.18.0)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from virtualenv>=20.10.0->pre-commit>=4->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 7)) (4.3.8)
Requirement already satisfied: python-dateutil>=2.8.2 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pandas->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pandas->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from pandas->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (2025.2)
Requirement already satisfied: six>=1.5 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from python-dateutil>=2.8.2->pandas->optimagic==0.5.2.dev38+g84c09d5.d20250707->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 8)) (1.17.0)
Requirement already satisfied: sphinxcontrib-applehelp in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp>=2.0.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml>=1.1.5 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (3.1.6)
Requirement already satisfied: docutils<0.18,>=0.14 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (0.17.1)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (3.0.1)
Requirement already satisfied: babel>=1.3 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (0.7.16)
Requirement already satisfied: imagesize in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (2.32.3)
Requirement already satisfied: MarkupSafe>=2.0 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from Jinja2>=2.3->sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (3.0.2)
Requirement already satisfied: charset_normalizer<4,>=2 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from requests>=2.5.0->sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from requests>=2.5.0->sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from requests>=2.5.0->sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/conda/envs/optimagic/lib/python3.10/site-packages (from requests>=2.5.0->sphinx->sphinxcontrib-mermaid->-r /workspaces/optimagic_/condaenv.4q5nexd8.requirements.txt (line 14)) (2025.6.15)
Building wheels for collected packages: optimagic
  Building editable for optimagic (pyproject.toml): started
  Building editable for optimagic (pyproject.toml): finished with status 'done'
  Created wheel for optimagic: filename=optimagic-0.5.2.dev38+g84c09d5.d20250707-py3-none-any.whl size=4212 sha256=1d2ec655c7c2dfa7e49214289006f370aff33f863fdb94fe2c42fc40572c2460
  Stored in directory: /tmp/pip-ephem-wheel-cache-ziv4r2vv/wheels/01/25/67/099a42f7bc3a365843a1d0cf927f7e7a6e21005a1d22fd9b06
Successfully built optimagic
Installing collected packages: optimagic
  Attempting uninstall: optimagic
    Found existing installation: optimagic 0.5.2.dev35+g8bffc41.d20250706
    Uninstalling optimagic-0.5.2.dev35+g8bffc41.d20250706:
      Successfully uninstalled optimagic-0.5.2.dev35+g8bffc41.d20250706
Successfully installed optimagic-0.5.2.dev38+g84c09d5.d20250707

done
#
# To activate this environment, use
#
#     $ conda activate optimagic
#
# To deactivate an active environment, use
#
#     $ conda deactivate

(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ conda activate optimagic
(optimagic) (base) @spline2hg ➜ /workspaces/optimagic_ (bayesian_optimizer) $ 