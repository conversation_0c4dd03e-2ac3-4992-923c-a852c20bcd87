# PyGAD GA Class Constructor Parameters

## Basic Parameters

- [X] `num_generations`: Number of generations.
- [x] `num_parents_mating`: Number of solutions to be selected as parents.

## Fitness Function Parameters

- [x] `fitness_batch_size`: Calculate fitness in batches. Default is `None`.
- [x] `fitness_func`: Function/method to return fitness values. Supports multi-objective optimization.

## Population Parameters

- [x] `initial_population`: User-defined initial population.
- [x] `sol_per_pop`: Number of solutions in the population.
- [x] `num_genes`: Number of genes in the solution/chromosome.
- [x] `keep_parents`: Number of parents to keep in the next generation.
- [x] `keep_elitism`: Number of best solutions to keep in the next generation.

## Gene Type and Initialization Parameters

- [x] `gene_type`: Data type for genes (float, int, etc.).
- [x] `init_range_low`: Lower range for initial gene values.
- [x] `init_range_high`: Upper range for initial gene values.

## Parent Selection Parameters

- [x] `parent_selection_type`: Type of parent selection (e.g., `sss`, `rws`, `tournament`).
- [x] `K_tournament`: Number of parents in tournament selection.

## Crossover Parameters

- [x] `crossover_type`: Type of crossover (e.g., `single_point`, `two_points`).
- [x] `crossover_probability`: Probability of applying crossover.

## Mutation Parameters

- [X] `mutation_type`: Type of mutation (e.g., `random`, `swap`, `adaptive`).
- [x] `mutation_probability`: Probability of applying mutation.
- [x] `mutation_by_replacement`: Whether to replace gene values during mutation.
- [x] `mutation_percent_genes`: Percentage of genes to mutate.
- [x] `mutation_num_genes`: Number of genes to mutate.
- [x] `random_mutation_min_val`: Minimum value for random mutation.
- [x] `random_mutation_max_val`: Maximum value for random mutation.

## Gene Space Parameters

- [x] `gene_space`: Possible values for each gene (range, list, etc.).

## Callback Functions

- [ ] `on_start`: Function called before evolution starts.
- [ ] `on_fitness`: Function called after calculating fitness.
- [ ] `on_parents`: Function called after selecting parents.
- [ ] `on_crossover`: Function called after crossover.
- [ ] `on_mutation`: Function called after mutation.
- [ ] `on_generation`: Function called after each generation.
- [ ] `on_stop`: Function called before stopping the algorithm.

## Additional Features

- [x] `save_best_solutions`: Save best solutions after each generation.
- [x] `save_solutions`: Save all solutions after each generation.
- [x] `suppress_warnings`: Suppress warning messages.
- [x] `allow_duplicate_genes`: Allow duplicate gene values in a solution.
- [x] `stop_criteria`: Criteria to stop the evolution (e.g., `reach`, `saturate`).
- [x] `parallel_processing`: Enable parallel processing (process/thread).
- [x] `random_seed`: Set random seed for reproducibility.
- [ ] `logger`: Logger instance for logging outputs.


## Constraint

- [ ] gene_constraint: constraints for the genes